# -*- coding: utf-8 -*-
# 外卖订单模块

import time
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Header
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.dao.account import account_dao
from app.service.order import order_service
from app.dao.order import order_dao
from app.service.fengniao import FengniaoClient
from app.service.coupon_calculator import coupon_calculator
from app.utils.logger import logger
from app.models.order import OrderType
from app.core.config import settings

router = APIRouter()


@router.post("/delivery-order/create")
async def create_delivery_order(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """创建外卖订单

    Args:
        task_info: 订单信息，包含：
            - order_type: 订单类型，固定为"delivery"
            - items: 商品列表，每个商品包含dish_id、name、price、quantity
            - total_amount: 订单总金额
            - delivery_address: 配送地址信息（必须包含latitude、longitude坐标）
            - delivery_time: 配送时间信息（可选）
            - coupon_id: 优惠券ID字符串，格式如"14,17"（可选）
            - coupon_discount: 预期优惠金额（可选）
            - delivery_fee: 配送费用（必须）
            - service_goods_id: 蜂鸟服务商品ID（必须）
            - base_goods_id: 蜂鸟基础商品ID（必须）
            - t_index_id: 蜂鸟运力索引ID（必须）
        token: 用户token
        db: 数据库会话

    Returns:
        返回格式示例:
        {
            "code": 200,
            "message": "success",
            "data": {
                "order_no": "O20231201120000123456",
                "order_id": 123,
                "payable_amount": 45.70,
                "user_balance": 100.00
            }
        }
    """
    logger.info(f"创建外卖订单，接收到的参数: {task_info}")
    
    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10] if token else 'None'}...")
            return {
                "code": 401,
                "message": "未登录",
                "data": None
            }
        
        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")
        
        # 提取优惠券参数
        coupon_id_str = task_info.get("coupon_id", "")
        coupon_discount = task_info.get("coupon_discount", 0)
        
        # 解析优惠券ID列表
        coupon_usage_record_ids = []
        if coupon_id_str and coupon_id_str.strip():
            try:
                coupon_usage_record_ids = [int(id.strip()) for id in coupon_id_str.split(",") if id.strip()]
                logger.info(f"解析优惠券ID列表: {coupon_usage_record_ids}")
            except ValueError as e:
                logger.error(f"优惠券ID格式错误: {coupon_id_str}, 错误: {str(e)}")
                return {
                    "code": 400,
                    "message": "优惠券ID格式错误",
                    "data": None
                }
        
        logger.info(f"优惠券参数 - coupon_usage_record_ids: {coupon_usage_record_ids}, coupon_discount: {coupon_discount}")
        
        # 验证必填参数
        required_fields = ['order_type', 'items', 'total_amount', 'delivery_address', 'delivery_fee', 'service_goods_id', 'base_goods_id', 't_index_id']

        for field in required_fields:
            if field not in task_info or task_info[field] is None:
                logger.error(f"参数错误，缺少必填字段: {field}")
                return {
                    "code": 400,
                    "message": f"缺少必填参数: {field}",
                    "data": None
                }
        
        # 验证订单类型
        if task_info['order_type'] != 'delivery':
            logger.error(f"订单类型错误: {task_info['order_type']}")
            return {
                "code": 400,
                "message": "订单类型必须为delivery",
                "data": None
            }
        
        # 验证商品列表
        items = task_info['items']
        if not items or not isinstance(items, list):
            logger.error("商品列表为空或格式错误")
            return {
                "code": 400,
                "message": "商品列表不能为空",
                "data": None
            }
        
        # 验证配送地址
        delivery_address = task_info['delivery_address']
        if not isinstance(delivery_address, dict):
            logger.error("配送地址格式错误")
            return {
                "code": 400,
                "message": "配送地址格式错误",
                "data": None
            }
        
        # 验证配送地址必填字段
        address_required_fields = ['name', 'phone', 'detail']
        for field in address_required_fields:
            if field not in delivery_address or not delivery_address[field]:
                logger.error(f"配送地址缺少必填字段: {field}")
                return {
                    "code": 400,
                    "message": f"配送地址缺少必填字段: {field}",
                    "data": None
                }
        
        # 构建产品列表
        products = []
        for item in items:
            # 验证商品必填字段
            item_required_fields = ['dish_id', 'quantity']
            for field in item_required_fields:
                if field not in item or item[field] is None:
                    logger.error(f"商品参数错误，缺少必填字段: {field}")
                    return {
                        "code": 400,
                        "message": f"商品缺少必填参数: {field}",
                        "data": None
                    }
            
            # 验证数量为正整数
            try:
                quantity = int(item['quantity'])
                if quantity <= 0:
                    raise ValueError("数量必须为正整数")
            except (ValueError, TypeError):
                logger.error(f"商品数量格式错误: {item.get('quantity')}")
                return {
                    "code": 400,
                    "message": "商品数量必须为正整数",
                    "data": None
                }
            
            products.append({
                "product_id": int(item["dish_id"]),
                "quantity": quantity
            })
        
        logger.info(f"创建商品列表: {products}")

        # 获取配送相关参数
        delivery_fee = float(task_info['delivery_fee'])
        service_goods_id = task_info['service_goods_id']
        base_goods_id = task_info['base_goods_id']
        t_index_id = task_info['t_index_id']

        logger.info(f"配送参数 - 配送费: {delivery_fee}, 服务商品ID: {service_goods_id}, 基础商品ID: {base_goods_id}, 运力索引ID: {t_index_id}")

        # 格式化配送地址和时间
        formatted_delivery_address = _format_delivery_address(delivery_address)
        formatted_delivery_time = _format_delivery_time(task_info.get('delivery_time'))

        logger.info(f"格式化配送地址: {formatted_delivery_address}")
        logger.info(f"格式化配送时间: {formatted_delivery_time}")

        # 使用pre_order方法进行预订单处理
        pre_order_result = order_service.pre_order(db, products)
        order_items = pre_order_result.order_items
        total_amount = pre_order_result.total_amount
        order_payable_amount = pre_order_result.order_payable_amount

        # 处理优惠券逻辑
        total_discount_amount = 0.0
        coupon_discounts = []
        if coupon_usage_record_ids:
            try:
                # 验证并计算优惠券优惠
                updated_order_items, total_discount_amount, coupon_discounts = coupon_calculator.validate_and_calculate_coupons(
                    db, user_id, order_items, coupon_usage_record_ids
                )
                order_items = updated_order_items

                # 重新计算订单应付金额
                order_payable_amount = sum(item.payable_amount for item in order_items)

                logger.info(f"优惠券计算完成 - 总优惠金额: {total_discount_amount}, 订单应付金额: {order_payable_amount}")

            except Exception as e:
                logger.error(f"优惠券计算失败: {str(e)}")
                return {
                    "code": 400,
                    "message": f"优惠券计算失败: {str(e)}",
                    "data": None
                }

        # 将订单项转换为字典格式
        items_data = []
        for item in order_items:
            items_data.append({
                "product_id": item.product_id,
                "quantity": item.quantity,
                "price": item.price,
                "subtotal": item.subtotal,
                "final_price": item.final_price,
                "payable_amount": item.payable_amount,
                "pricing_remark": item.pricing_remark or ""
            })

        # 创建外卖订单，包含配送信息
        order = order_dao.create_order_by_type(
            session=db,
            order_type=OrderType.DELIVERY,
            user_id=user_id,
            total_amount=total_amount,
            payable_amount=order_payable_amount,
            actual_amount_paid=0.0,
            discount_amount=total_discount_amount,
            items=items_data,
            delivery_address_raw=delivery_address,
            delivery_address=formatted_delivery_address,
            delivery_time_raw=task_info.get('delivery_time'),
            delivery_time=formatted_delivery_time,
            delivery_fee=delivery_fee,
            fengniao_order_id=None,  # 暂时为空，后续创建蜂鸟订单时更新
            fengniao_status=None
        )

        order_id = order.id
        order_no = order.order_no
        order_payable_amount = order.payable_amount

        logger.info(f"创建订单: {order}")
        logger.info(f"创建的订单号: {order_no}")
        logger.info(f"创建的订单金额: {order_payable_amount}")
        logger.info(f"订单优惠金额: {total_discount_amount}")

        # 更新优惠券使用记录
        if coupon_usage_record_ids and coupon_discounts:
            try:
                coupon_calculator.update_coupon_usage_records(
                    db, order_id, coupon_usage_record_ids, coupon_discounts
                )
                logger.info(f"已更新{len(coupon_usage_record_ids)}个优惠券使用记录")
            except Exception as e:
                logger.error(f"更新优惠券使用记录失败: {str(e)}")
                # 这里可以选择是否回滚订单创建，根据业务需求决定

        # 验证优惠金额
        if coupon_usage_record_ids and coupon_discount > 0:
            if abs(total_discount_amount - coupon_discount) > 0.01:  # 允许0.01的误差
                logger.error(f"优惠金额不匹配 - 预期: {coupon_discount}, 实际: {total_discount_amount}")
                db.rollback()
                return {
                    "code": 400,
                    "message": f"优惠金额验证失败，预期优惠{coupon_discount}元，实际优惠{total_discount_amount}元",
                    "data": None
                }
            logger.info(f"优惠金额验证通过 - 预期: {coupon_discount}, 实际: {total_discount_amount}")

        # 获取用户余额
        user_balance = account_dao.get_user_balance(db, user_id)
        logger.info(f"用户余额: {user_balance}")

        return {
            "code": 200,
            "message": "success",
            "data": {
                'order_no': order_no,
                'order_id': order_id,
                'payable_amount': order_payable_amount,
                'user_balance': user_balance
            }
        }
        
    except Exception as e:
        logger.error(f"创建外卖订单失败: {str(e)}", exc_info=True)
        db.rollback()
        return {
            "code": 500,
            "message": f"创建订单失败: {str(e)}",
            "data": None
        }


@router.post("/delivery-order/delivery-fee")
async def delivery_fee(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取外卖配送价格（预下单接口）

    Args:
        task_info: 订单信息，包含：
            - order_type: 订单类型，固定为"delivery"
            - items: 商品列表，每个商品包含dish_id、name、price、quantity
            - total_amount: 订单总金额
            - delivery_address: 配送地址信息（必须包含latitude、longitude坐标）
            - delivery_time: 配送时间信息（可选）
        token: 用户token
        db: 数据库会话

    Returns:
        返回格式示例:
        {
            "code": 200,
            "message": "success",
            "data": {
                "delivery_fee": 11.70,
                "delivery_fee_cent": 1170,
                "predict_delivery_minutes": 64,
                "distance": 4447,
                "service_goods_id": 3008,
                "base_goods_id": 30024,
                "slogan": "24小时服务，灵活配送准点达",
                "t_index_id": "b69d90c2-b7f0-4f0b-a62c-4ad64aa50835"
            }
        }
    """
    logger.info(f"查询外卖费用，接收到的参数: {task_info}")

    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10] if token else 'None'}...")
            return {
                "code": 401,
                "message": "未登录",
                "data": None
            }

        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")

        # 验证必填参数
        required_fields = ['order_type', 'items', 'total_amount', 'delivery_address']

        for field in required_fields:
            if field not in task_info or task_info[field] is None:
                logger.error(f"参数错误，缺少必填字段: {field}")
                return {
                    "code": 400,
                    "message": f"缺少必填参数: {field}",
                    "data": None
                }

        # 验证订单类型
        if task_info['order_type'] != 'delivery':
            logger.error(f"订单类型错误: {task_info['order_type']}")
            return {
                "code": 400,
                "message": "订单类型必须为delivery",
                "data": None
            }

        # 验证商品列表
        items = task_info['items']
        if not items or not isinstance(items, list):
            logger.error("商品列表为空或格式错误")
            return {
                "code": 400,
                "message": "商品列表不能为空",
                "data": None
            }

        # 验证配送地址
        delivery_address = task_info['delivery_address']
        if not isinstance(delivery_address, dict):
            logger.error("配送地址格式错误")
            return {
                "code": 400,
                "message": "配送地址格式错误",
                "data": None
            }

        # 验证配送地址必填字段
        address_required_fields = ['name', 'phone', 'detail']
        for field in address_required_fields:
            if field not in delivery_address or not delivery_address[field]:
                logger.error(f"配送地址缺少必填字段: {field}")
                return {
                    "code": 400,
                    "message": f"配送地址缺少必填字段: {field}",
                    "data": None
                }

        # 构建产品列表
        products = []
        for item in items:
            # 验证商品必填字段
            item_required_fields = ['dish_id', 'quantity']
            for field in item_required_fields:
                if field not in item or item[field] is None:
                    logger.error(f"商品参数错误，缺少必填字段: {field}")
                    return {
                        "code": 400,
                        "message": f"商品缺少必填参数: {field}",
                        "data": None
                    }

            # 验证数量为正整数
            try:
                quantity = int(item['quantity'])
                if quantity <= 0:
                    raise ValueError("数量必须为正整数")
            except (ValueError, TypeError):
                logger.error(f"商品数量格式错误: {item.get('quantity')}")
                return {
                    "code": 400,
                    "message": "商品数量必须为正整数",
                    "data": None
                }

            products.append({
                "product_id": int(item["dish_id"]),
                "quantity": quantity
            })

        logger.info(f"创建商品列表: {products}")

        # 准备蜂鸟预下单数据
        fengniao_order_data = _prepare_fengniao_pre_order_data(task_info, delivery_address)
        logger.info(f"准备蜂鸟预下单数据: {fengniao_order_data}")

        # 调用蜂鸟API预下单接口获取价格
        fengniao_client = FengniaoClient()
        logger.info("开始调用蜂鸟预下单接口...")
        fengniao_result = fengniao_client.pre_create_order(fengniao_order_data)

        logger.info(f"蜂鸟预下单API响应: {fengniao_result}")

        # 检查蜂鸟API调用结果
        if fengniao_result.get("code") != "200":
            logger.error(f"蜂鸟预下单失败: {fengniao_result.get('msg', '未知错误')}")
            return {
                "code": 500,
                "message": f"获取配送价格失败: {fengniao_result.get('msg', '未知错误')}",
                "data": None
            }

        # 解析预下单结果，选择最便宜的运力
        business_data = fengniao_result.get("business_data")
        if business_data:
            import json
            if isinstance(business_data, str):
                business_data = json.loads(business_data)

            goods_infos = business_data.get("goods_infos", [])
            logger.info(f"获取到运力列表，共 {len(goods_infos)} 个选项")

            # 筛选可用的运力并找到最便宜的
            available_goods = [goods for goods in goods_infos if goods.get("is_valid") == 1]
            logger.info(f"可用运力数量: {len(available_goods)}")

            if not available_goods:
                logger.error("没有可用的配送运力")
                return {
                    "code": 500,
                    "message": "当前区域暂无可用配送服务",
                    "data": None
                }

            # 选择价格最便宜的运力
            cheapest_goods = min(available_goods, key=lambda x: x.get("actual_delivery_amount_cent", float('inf')))
            delivery_fee_cent = cheapest_goods.get("actual_delivery_amount_cent", 0)
            delivery_fee = delivery_fee_cent / 100.0  # 转换为元

            logger.info(f"选择最便宜的运力:")
            logger.info(f"  - 服务商品ID: {cheapest_goods.get('service_goods_id')}")
            logger.info(f"  - 基础商品ID: {cheapest_goods.get('base_goods_id')}")
            logger.info(f"  - 配送费(分): {delivery_fee_cent}")
            logger.info(f"  - 配送费(元): {delivery_fee}")
            logger.info(f"  - 预计送达时间(分钟): {cheapest_goods.get('predict_delivery_minutes')}")
            logger.info(f"  - 商品介绍: {cheapest_goods.get('slogan')}")
            logger.info(f"  - 配送距离(米): {business_data.get('distance')}")

            return {
                "code": 200,
                "message": "success",
                "data": {
                    "delivery_fee": delivery_fee,
                    "delivery_fee_cent": delivery_fee_cent,
                    "predict_delivery_minutes": cheapest_goods.get("predict_delivery_minutes"),
                    "distance": business_data.get("distance"),
                    "service_goods_id": cheapest_goods.get("service_goods_id"),
                    "base_goods_id": cheapest_goods.get("base_goods_id"),
                    "slogan": cheapest_goods.get("slogan"),
                    "t_index_id": cheapest_goods.get("t_index_id")
                }
            }
        else:
            logger.error("蜂鸟预下单返回数据格式错误，缺少business_data")
            return {
                "code": 500,
                "message": "获取配送价格失败，返回数据格式错误",
                "data": None
            }

    except Exception as e:
        logger.error(f"获取配送价格失败: {str(e)}", exc_info=True)
        db.rollback()
        return {
            "code": 500,
            "message": f"获取配送价格失败: {str(e)}",
            "data": None
        }


def _prepare_fengniao_pre_order_data(task_info: dict, delivery_address: dict) -> dict:
    """准备蜂鸟预下单数据，根据配置使用门店发单或点对点配送方式"""

    # 获取当前时间戳（毫秒）
    current_timestamp = int(time.time() * 1000)

    # 生成临时订单号用于预下单
    temp_order_code = f"pre_{int(time.time())}_{int(time.time() * 1000) % 1000}"

    # 构建商品列表
    goods_item_list = []
    for item in task_info['items']:
        goods_item_list.append({
            "item_id": str(item['dish_id']),
            "item_name": item.get('name', '商品'),
            "item_quantity": int(item['quantity']),
            "item_amount_cent": int(float(item.get('price', 0)) * 100),  # 转换为分
            "item_actual_amount_cent": int(float(item.get('price', 0)) * int(item['quantity']) * 100)  # 转换为分
        })

    # 构建蜂鸟预下单基础数据
    fengniao_data = {
        "partner_order_code": temp_order_code,  # 临时订单号
        "receiver_address": delivery_address['detail'],
        "receiver_latitude": delivery_address.get('latitude', 0.0),  # 需要前端提供
        "receiver_longitude": delivery_address.get('longitude', 0.0),  # 需要前端提供
        "goods_count": len(task_info['items']),
        "goods_weight": 1.0,  # 默认重量，可以根据实际情况调整
        "goods_total_amount_cent": int(float(task_info['total_amount']) * 100),  # 转换为分
        "goods_actual_amount_cent": int(float(task_info['total_amount']) * 100),  # 预下单时使用原始金额
        "goods_item_list": goods_item_list,
        "order_type": 1,  # 1:即时单，3:预约单
        "position_source": 3,  # 3:高德地图
    }

    # 根据配置的配送模式添加相应参数
    delivery_mode = settings.FENGNIAO_DELIVERY_MODE.lower()

    if delivery_mode == "store":
        # 门店发单模式：使用out_shop_code或chain_store_id
        if settings.FENGNIAO_CHAIN_STORE_ID:
            fengniao_data["chain_store_id"] = int(settings.FENGNIAO_CHAIN_STORE_ID)
            logger.info(f"使用门店发单模式 - chain_store_id: {settings.FENGNIAO_CHAIN_STORE_ID}")
        else:
            fengniao_data["out_shop_code"] = settings.FENGNIAO_SHOP_ID
            logger.info(f"使用门店发单模式 - out_shop_code: {settings.FENGNIAO_SHOP_ID}")

    elif delivery_mode == "point_to_point":
        # 点对点配送模式：添加取货点信息
        fengniao_data.update({
            "transport_longitude": settings.FENGNIAO_TRANSPORT_LONGITUDE,  # 取货点经度
            "transport_latitude": settings.FENGNIAO_TRANSPORT_LATITUDE,   # 取货点纬度
            "transport_address": settings.FENGNIAO_TRANSPORT_ADDRESS,  # 取货点地址描述
            "transport_tel": settings.FENGNIAO_TRANSPORT_TEL,    # 取货点联系电话
        })
        logger.info(f"使用点对点配送模式")
        logger.info(f"  - 取货点坐标: ({settings.FENGNIAO_TRANSPORT_LONGITUDE}, {settings.FENGNIAO_TRANSPORT_LATITUDE})")
        logger.info(f"  - 取货点地址: {settings.FENGNIAO_TRANSPORT_ADDRESS}")
        logger.info(f"  - 取货点电话: {settings.FENGNIAO_TRANSPORT_TEL}")

    else:
        # 默认使用门店发单模式
        fengniao_data["out_shop_code"] = settings.FENGNIAO_SHOP_ID
        logger.warning(f"未知的配送模式: {delivery_mode}，默认使用门店发单模式")
        logger.info(f"使用门店发单模式 - out_shop_code: {settings.FENGNIAO_SHOP_ID}")

    # 如果有配送时间要求，设置为预约单
    delivery_time = task_info.get('delivery_time')
    if delivery_time and delivery_time.get('value'):
        fengniao_data["order_type"] = 3  # 预约单
        # 这里需要根据delivery_time计算expect_fetch_time和require_receive_time
        # 暂时使用当前时间+30分钟作为出餐时间，+60分钟作为送达时间
        fengniao_data["expect_fetch_time"] = current_timestamp + (30 * 60 * 1000)
        fengniao_data["require_receive_time"] = current_timestamp + (60 * 60 * 1000)

    logger.info(f"构建蜂鸟预下单数据 ({delivery_mode}模式):")
    logger.info(f"  - 临时订单号: {temp_order_code}")
    logger.info(f"  - 配送模式: {delivery_mode}")
    logger.info(f"  - 收货点坐标: ({fengniao_data['receiver_longitude']}, {fengniao_data['receiver_latitude']})")
    logger.info(f"  - 收货点地址: {fengniao_data['receiver_address']}")
    logger.info(f"  - 商品总金额: {fengniao_data['goods_total_amount_cent']}分")
    logger.info(f"  - 商品数量: {fengniao_data['goods_count']}")
    logger.info(f"  - 订单类型: {fengniao_data['order_type']} ({'即时单' if fengniao_data['order_type'] == 1 else '预约单'})")

    return fengniao_data


def _format_delivery_address(delivery_address: dict) -> str:
    """格式化配送地址为字符串"""
    try:
        # 构建完整地址字符串
        address_parts = []

        # 添加省市区信息
        if delivery_address.get('province'):
            address_parts.append(delivery_address['province'])
        if delivery_address.get('city'):
            address_parts.append(delivery_address['city'])
        if delivery_address.get('district'):
            address_parts.append(delivery_address['district'])

        # 添加详细地址
        if delivery_address.get('detail'):
            address_parts.append(delivery_address['detail'])

        # 拼接地址
        formatted_address = ''.join(address_parts)

        # 添加收货人信息
        if delivery_address.get('name'):
            formatted_address += f" ({delivery_address['name']}"
            if delivery_address.get('phone'):
                formatted_address += f" {delivery_address['phone']}"
            formatted_address += ")"

        return formatted_address
    except Exception as e:
        logger.error(f"格式化配送地址失败: {str(e)}")
        return str(delivery_address)


def _format_delivery_time(delivery_time_data: dict) -> Optional[datetime]:
    """格式化配送时间为datetime对象"""
    try:
        if not delivery_time_data:
            return None

        # 如果有具体的时间值，尝试解析
        time_value = delivery_time_data.get('value')
        if time_value:
            # 这里需要根据实际的时间格式进行解析
            # 假设时间格式为 "12:00-15:00" 这样的时间段
            # 我们取开始时间作为配送时间
            if '-' in time_value:
                start_time = time_value.split('-')[0].strip()
                # 构建今天的这个时间
                from datetime import datetime, time as dt_time
                today = datetime.now().date()
                try:
                    hour, minute = map(int, start_time.split(':'))
                    delivery_datetime = datetime.combine(today, dt_time(hour, minute))
                    return delivery_datetime
                except ValueError:
                    logger.warning(f"无法解析时间格式: {start_time}")
                    return None

        return None
    except Exception as e:
        logger.error(f"格式化配送时间失败: {str(e)}")
        return None
