"""add_fengniao_fields

Revision ID: 902d13b1a080
Revises: c8a2e2c6c8f2
Create Date: 2025-09-29 12:32:03.791713

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '902d13b1a080'
down_revision: Union[str, None] = 'c8a2e2c6c8f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('delivery_orders', sa.Column('service_goods_id', sa.Integer(), nullable=True, comment='蜂鸟服务商品ID'))
    op.add_column('delivery_orders', sa.Column('base_goods_id', sa.Integer(), nullable=True, comment='蜂鸟基础商品ID'))
    op.add_column('delivery_orders', sa.Column('t_index_id', sa.String(length=128), nullable=True, comment='蜂鸟预询标识'))
    op.alter_column('orders', 'type',
               existing_type=mysql.ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', 'DELIVERY', collation='utf8mb4_unicode_ci'),
               nullable=False,
               comment='订单类型')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('orders', 'type',
               existing_type=mysql.ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', 'DELIVERY', collation='utf8mb4_unicode_ci'),
               nullable=True,
               comment=None,
               existing_comment='订单类型')
    op.drop_column('delivery_orders', 't_index_id')
    op.drop_column('delivery_orders', 'base_goods_id')
    op.drop_column('delivery_orders', 'service_goods_id')
    # ### end Alembic commands ###
