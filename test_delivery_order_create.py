#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试外卖订单创建API的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000"

def test_create_delivery_order():
    """测试创建外卖订单"""
    
    # 测试数据
    test_data = {
        "order_type": "delivery",
        "items": [
            {
                "dish_id": 1,
                "name": "测试商品1",
                "price": 25.00,
                "quantity": 2
            },
            {
                "dish_id": 2,
                "name": "测试商品2", 
                "price": 15.00,
                "quantity": 1
            }
        ],
        "total_amount": 65.00,  # 商品总金额，不包含配送费
        "delivery_address": {
            "name": "张三",
            "phone": "13800138000",
            "detail": "北京市朝阳区测试街道123号",
            "latitude": 39.9042,
            "longitude": 116.4074,
            "province": "北京市",
            "city": "北京市",
            "district": "朝阳区"
        },
        "delivery_time": {
            "value": "12:00-13:00",
            "label": "中午12:00-13:00"
        },
        "delivery_fee": 8.50,  # 配送费，会加到最终应付金额中
        "service_goods_id": 3008,
        "base_goods_id": 30024,
        "t_index_id": "b69d90c2-b7f0-4f0b-a62c-4ad64aa50835",
        "coupon_id": "",  # 可选，测试时可以留空
        "coupon_discount": 0  # 可选，测试时可以为0
    }
    
    # 请求头（需要有效的token）
    headers = {
        "Content-Type": "application/json",
        "token": "your_test_token_here"  # 需要替换为有效的token
    }
    
    # 发送请求
    url = f"{BASE_URL}/api/v1/wechat-mini-app/delivery-order/create"
    
    print("=== 测试创建外卖订单 ===")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 订单创建成功!")
                data = result.get("data", {})
                print(f"订单号: {data.get('order_no')}")
                print(f"订单ID: {data.get('order_id')}")
                print(f"应付金额: {data.get('payable_amount')} (包含配送费)")
                print(f"用户余额: {data.get('user_balance')}")
            else:
                print(f"❌ 订单创建失败: {result.get('message')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {str(e)}")
        print(f"原始响应: {response.text}")

def test_create_delivery_order_with_coupon():
    """测试使用优惠券创建外卖订单"""
    
    # 测试数据（包含优惠券）
    test_data = {
        "order_type": "delivery",
        "items": [
            {
                "dish_id": 1,
                "name": "测试商品1",
                "price": 25.00,
                "quantity": 2
            }
        ],
        "total_amount": 50.00,
        "delivery_address": {
            "name": "李四",
            "phone": "13900139000",
            "detail": "上海市浦东新区测试路456号",
            "latitude": 31.2304,
            "longitude": 121.4737,
            "province": "上海市",
            "city": "上海市",
            "district": "浦东新区"
        },
        "delivery_fee": 6.00,
        "service_goods_id": 3008,
        "base_goods_id": 30024,
        "t_index_id": "test-index-id-123",
        "coupon_id": "14,17",  # 使用优惠券ID
        "coupon_discount": 5.00  # 预期优惠5元
    }
    
    # 请求头（需要有效的token）
    headers = {
        "Content-Type": "application/json",
        "token": "your_test_token_here"  # 需要替换为有效的token
    }
    
    # 发送请求
    url = f"{BASE_URL}/api/v1/wechat-mini-app/delivery-order/create"
    
    print("\n=== 测试使用优惠券创建外卖订单 ===")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 使用优惠券的订单创建成功!")
                data = result.get("data", {})
                print(f"订单号: {data.get('order_no')}")
                print(f"订单ID: {data.get('order_id')}")
                print(f"应付金额: {data.get('payable_amount')}")
                print(f"用户余额: {data.get('user_balance')}")
            else:
                print(f"❌ 订单创建失败: {result.get('message')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {str(e)}")
        print(f"原始响应: {response.text}")

if __name__ == "__main__":
    print("外卖订单创建API测试")
    print("=" * 50)
    
    # 测试基本订单创建
    test_create_delivery_order()
    
    # 测试使用优惠券的订单创建
    test_create_delivery_order_with_coupon()
    
    print("\n测试完成!")
