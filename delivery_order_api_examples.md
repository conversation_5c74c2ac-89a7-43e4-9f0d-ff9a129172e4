# 外卖订单创建API返回结果示例

## API接口信息

**接口路径**: `/api/v1/wechat-mini-app/delivery-order/create`
**请求方法**: POST
**认证方式**: Header中的token

## 请求参数示例

```json
{
    "order_type": "delivery",
    "items": [
        {
            "dish_id": 1,
            "name": "宫保鸡丁",
            "price": 28.00,
            "quantity": 1
        },
        {
            "dish_id": 5,
            "name": "麻婆豆腐",
            "price": 18.00,
            "quantity": 2
        }
    ],
    "total_amount": 64.00,  // 商品总金额，不包含配送费
    "delivery_address": {
        "name": "张三",
        "phone": "13800138000",
        "detail": "北京市朝阳区建国路88号SOHO现代城A座1201室",
        "latitude": 39.9042,
        "longitude": 116.4074,
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区"
    },
    "delivery_time": {
        "value": "12:00-13:00",
        "label": "中午12:00-13:00"
    },
    "delivery_fee": 8.50,  // 配送费，会加到最终应付金额中
    "service_goods_id": 3008,
    "base_goods_id": 30024,
    "t_index_id": "b69d90c2-b7f0-4f0b-a62c-4ad64aa50835",
    "coupon_id": "14,17",
    "coupon_discount": 5.00
}
```

## 成功响应示例

### 1. 不使用优惠券的订单创建成功

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "order_no": "O20231201120000123456",
        "order_id": 1001,
        "payable_amount": 72.50,  // 64.00(商品) + 8.50(配送费) = 72.50
        "user_balance": 150.00
    }
}
```

### 2. 使用优惠券的订单创建成功

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "order_no": "O20231201120500789012",
        "order_id": 1002,
        "payable_amount": 67.50,  // 64.00(商品) + 8.50(配送费) - 5.00(优惠) = 67.50
        "user_balance": 150.00
    }
}
```

## 错误响应示例

### 1. 用户未登录

```json
{
    "code": 401,
    "message": "未登录",
    "data": null
}
```

### 2. 缺少必填参数

```json
{
    "code": 400,
    "message": "缺少必填参数: delivery_fee",
    "data": null
}
```

### 3. 订单类型错误

```json
{
    "code": 400,
    "message": "订单类型必须为delivery",
    "data": null
}
```

### 4. 商品列表为空

```json
{
    "code": 400,
    "message": "商品列表不能为空",
    "data": null
}
```

### 5. 配送地址格式错误

```json
{
    "code": 400,
    "message": "配送地址缺少必填字段: phone",
    "data": null
}
```

### 6. 商品数量格式错误

```json
{
    "code": 400,
    "message": "商品数量必须为正整数",
    "data": null
}
```

### 7. 优惠券ID格式错误

```json
{
    "code": 400,
    "message": "优惠券ID格式错误",
    "data": null
}
```

### 8. 优惠券计算失败

```json
{
    "code": 400,
    "message": "优惠券计算失败: 优惠券已过期",
    "data": null
}
```

### 9. 优惠金额验证失败

```json
{
    "code": 400,
    "message": "优惠金额验证失败，预期优惠5.00元，实际优惠3.50元",
    "data": null
}
```

### 10. 系统内部错误

```json
{
    "code": 500,
    "message": "创建订单失败: 数据库连接异常",
    "data": null
}
```

## 字段说明

### 请求参数字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_type | string | 是 | 订单类型，固定为"delivery" |
| items | array | 是 | 商品列表 |
| items[].dish_id | integer | 是 | 商品ID |
| items[].name | string | 否 | 商品名称 |
| items[].price | number | 否 | 商品单价 |
| items[].quantity | integer | 是 | 商品数量 |
| total_amount | number | 是 | 商品总金额（不包含配送费） |
| delivery_address | object | 是 | 配送地址信息 |
| delivery_address.name | string | 是 | 收货人姓名 |
| delivery_address.phone | string | 是 | 收货人电话 |
| delivery_address.detail | string | 是 | 详细地址 |
| delivery_address.latitude | number | 否 | 纬度 |
| delivery_address.longitude | number | 否 | 经度 |
| delivery_time | object | 否 | 配送时间信息 |
| delivery_fee | number | 是 | 配送费用 |
| service_goods_id | integer | 是 | 蜂鸟服务商品ID |
| base_goods_id | integer | 是 | 蜂鸟基础商品ID |
| t_index_id | string | 是 | 蜂鸟运力索引ID |
| coupon_id | string | 否 | 优惠券ID字符串，多个用逗号分隔 |
| coupon_discount | number | 否 | 预期优惠金额 |

### 响应数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，200表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.order_no | string | 订单号 |
| data.order_id | integer | 订单ID |
| data.payable_amount | number | 订单应付金额（包含配送费和优惠后的最终金额） |
| data.user_balance | number | 用户账户余额 |

## 业务逻辑说明

1. **订单创建流程**：
   - 验证用户token
   - 验证必填参数
   - 解析优惠券信息
   - 验证商品信息
   - 验证配送地址
   - 进行预订单处理（库存检查、计价）
   - 处理优惠券逻辑
   - 创建外卖订单
   - 更新优惠券使用记录
   - 返回订单信息

2. **优惠券处理**：
   - 支持多张优惠券同时使用
   - 自动验证优惠券有效性
   - 计算实际优惠金额
   - 验证预期优惠金额与实际优惠金额的一致性

3. **配送信息处理**：
   - 格式化配送地址为字符串
   - 解析配送时间信息
   - 保存蜂鸟配送相关参数

4. **配送费计算**：
   - 订单应付金额 = 商品应付金额（优惠后） + 配送费
   - 配送费不参与优惠券计算，始终全额收取
   - 最终用户需要支付的金额包含配送费

5. **错误处理**：
   - 参数验证错误返回400状态码
   - 认证失败返回401状态码
   - 系统异常返回500状态码
   - 所有错误都会记录日志
