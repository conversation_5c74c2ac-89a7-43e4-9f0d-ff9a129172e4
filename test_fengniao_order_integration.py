#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蜂鸟下单集成测试脚本

测试外卖订单支付成功后的蜂鸟下单逻辑
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8000"  # 根据实际情况修改
TEST_TOKEN = "your_test_token_here"  # 需要替换为有效的测试token

def test_delivery_order_creation():
    """测试外卖订单创建"""
    print("=== 测试外卖订单创建 ===")
    
    url = f"{BASE_URL}/api/v1/wechat_mini_app/delivery/create_delivery_order"
    headers = {
        "Content-Type": "application/json",
        "token": TEST_TOKEN
    }
    
    # 测试数据
    test_data = {
        "order_type": "delivery",
        "items": [
            {
                "dish_id": 1,
                "name": "测试商品1",
                "price": 25.00,
                "quantity": 2
            },
            {
                "dish_id": 2,
                "name": "测试商品2", 
                "price": 15.00,
                "quantity": 1
            }
        ],
        "total_amount": 65.00,  # 商品总金额，不包含配送费
        "delivery_address": {
            "name": "张三",
            "phone": "13800138000",
            "detail": "北京市朝阳区测试街道123号",
            "latitude": 39.9042,
            "longitude": 116.4074,
            "province": "北京市",
            "city": "北京市",
            "district": "朝阳区"
        },
        "delivery_time": {
            "value": "12:00-13:00",
            "label": "中午12:00-13:00"
        },
        "delivery_fee": 8.50,  # 配送费，会加到最终应付金额中
        "service_goods_id": 3008,
        "base_goods_id": 30024,
        "t_index_id": "b69d90c2-b7f0-4f0b-a62c-4ad64aa50835",
        "coupon_id": "",  # 可选，测试时可以留空
        "coupon_discount": 0  # 可选，测试时可以为0
    }
    
    try:
        response = requests.post(url, headers=headers, json=test_data, timeout=30)
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                order_data = result.get("data", {})
                order_no = order_data.get("order_no")
                order_id = order_data.get("order_id")
                payable_amount = order_data.get("payable_amount")
                
                print(f"✅ 外卖订单创建成功")
                print(f"   订单号: {order_no}")
                print(f"   订单ID: {order_id}")
                print(f"   应付金额: {payable_amount}")
                
                return {
                    "success": True,
                    "order_no": order_no,
                    "order_id": order_id,
                    "payable_amount": payable_amount
                }
            else:
                print(f"❌ 外卖订单创建失败: {result.get('message')}")
                return {"success": False, "message": result.get('message')}
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return {"success": False, "message": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return {"success": False, "message": str(e)}


def test_balance_payment(order_no, payable_amount):
    """测试余额支付（会触发蜂鸟下单）"""
    print(f"\n=== 测试余额支付（订单号: {order_no}）===")
    
    url = f"{BASE_URL}/api/v1/wechat_mini_app/order/pay/create"
    headers = {
        "Content-Type": "application/json",
        "token": TEST_TOKEN
    }
    
    payment_data = {
        "order_no": order_no,
        "amount": payable_amount,
        "paymentMethod": "balance"
    }
    
    try:
        response = requests.post(url, headers=headers, json=payment_data, timeout=30)
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == 200:
                print(f"✅ 余额支付成功")
                print(f"   消息: {result.get('message')}")
                return {"success": True}
            else:
                print(f"❌ 余额支付失败: {result.get('message')}")
                return {"success": False, "message": result.get('message')}
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return {"success": False, "message": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return {"success": False, "message": str(e)}


def test_wechat_payment(order_no, payable_amount):
    """测试微信支付（生成支付参数）"""
    print(f"\n=== 测试微信支付（订单号: {order_no}）===")
    
    url = f"{BASE_URL}/api/v1/wechat_mini_app/order/pay/create"
    headers = {
        "Content-Type": "application/json",
        "token": TEST_TOKEN
    }
    
    payment_data = {
        "order_no": order_no,
        "amount": payable_amount,
        "paymentMethod": "wxpay"
    }
    
    try:
        response = requests.post(url, headers=headers, json=payment_data, timeout=30)
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == 200:
                print(f"✅ 微信支付参数生成成功")
                print(f"   消息: {result.get('message')}")
                pay_params = result.get("payParams", {})
                print(f"   支付参数: {json.dumps(pay_params, ensure_ascii=False, indent=2)}")
                return {"success": True, "payParams": pay_params}
            else:
                print(f"❌ 微信支付参数生成失败: {result.get('message')}")
                return {"success": False, "message": result.get('message')}
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return {"success": False, "message": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return {"success": False, "message": str(e)}


def main():
    """主测试流程"""
    print("🚀 开始蜂鸟下单集成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试服务器: {BASE_URL}")
    print("-" * 50)
    
    # 1. 创建外卖订单
    order_result = test_delivery_order_creation()
    if not order_result["success"]:
        print("❌ 外卖订单创建失败，终止测试")
        return
    
    order_no = order_result["order_no"]
    order_id = order_result["order_id"]
    payable_amount = order_result["payable_amount"]
    
    # 等待一下，确保订单创建完成
    time.sleep(2)
    
    # 2. 测试余额支付（会触发蜂鸟下单）
    print(f"\n📝 注意：余额支付会触发蜂鸟下单逻辑")
    print(f"   - 如果蜂鸟参数正确且价格一致，应该下单成功")
    print(f"   - 如果配送费发生变动，会返回'配送费发生变动，支付失败'")
    
    balance_result = test_balance_payment(order_no, payable_amount)
    
    # 3. 测试微信支付（生成支付参数，实际支付成功后会触发蜂鸟下单）
    print(f"\n📝 注意：微信支付只生成支付参数，实际支付成功后才会触发蜂鸟下单")
    print(f"   - 需要通过微信支付回调来触发蜂鸟下单逻辑")
    print(f"   - 如果配送费发生变动，会发起退款操作")
    
    # 创建新订单用于微信支付测试
    order_result2 = test_delivery_order_creation()
    if order_result2["success"]:
        wechat_result = test_wechat_payment(order_result2["order_no"], order_result2["payable_amount"])
    
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    print(f"   外卖订单创建: {'✅ 成功' if order_result['success'] else '❌ 失败'}")
    print(f"   余额支付: {'✅ 成功' if balance_result['success'] else '❌ 失败'}")
    if 'wechat_result' in locals():
        print(f"   微信支付参数: {'✅ 成功' if wechat_result['success'] else '❌ 失败'}")
    
    print("\n📋 后续测试建议:")
    print("   1. 检查数据库中外卖订单的蜂鸟相关字段是否正确保存")
    print("   2. 查看应用日志中的蜂鸟API调用记录")
    print("   3. 测试配送费变动的情况（修改t_index_id为无效值）")
    print("   4. 测试微信支付回调触发蜂鸟下单的完整流程")


if __name__ == "__main__":
    main()
